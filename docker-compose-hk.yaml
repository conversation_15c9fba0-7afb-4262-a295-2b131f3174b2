services:
  app1:
    build: .
    env_file: .env
    deploy:
      resources:
        limits:
          cpus: "0.8"  # Reduced for single-CPU HK server
          memory: 2g   # Reduced memory allocation
    ports:
      - "3000:3000"
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/healthz"]
      interval: 30s  # Increased for HK server
      timeout: 15s   # Increased timeout for HK
      retries: 5     # More retries for HK
      start_period: 45s  # Longer start period for HK
    # Add extra_hosts to allow container to access host services
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - INSTANCE_ID=hk-1
      - REGION=hk

      # CRITICAL FIX: Multi-region read/write splitting configuration
      # Write operations MUST go to US primary database (*************)
      # Read operations can use local HK replica (host.docker.internal)
      - DB_WRITE_HOST=*************
      - DB_WRITE_PORT=5432
      - DB_WRITE_DATABASE=tempfly_app
      - DB_WRITE_USER=postgres
      - DB_WRITE_PASSWORD=4wyWCAAk92hkGUhdh7
      - DB_WRITE_SSLMODE=disable

      # Local HK read replica for faster read operations
      - DB_READ_HOST=host.docker.internal
      - DB_READ_PORT=5432
      - DB_READ_DATABASE=tempfly_app
      - DB_READ_USER=postgres
      - DB_READ_PASSWORD=4wyWCAAk92hkGUhdh7
      - DB_READ_SSLMODE=disable

      # Legacy environment variables for backward compatibility
      - PGHOST=host.docker.internal
      - PGPORT=5432
      - PGDATABASE=tempfly_app
      - PGUSER=postgres
      - PGPASSWORD=4wyWCAAk92hkGUhdh7
      - PGSSLMODE=${PGSSLMODE:-disable}
      - DATABASE_URL=${DATABASE_URL}
      # HK-specific database connection settings (higher timeouts)
      - DB_CONNECTION_TIMEOUT=25000  # 25s for HK (vs 10s default)
      - DB_ACQUIRE_TIMEOUT=12500     # 12.5s for HK (vs 5s default)
      - DB_STATEMENT_TIMEOUT=112500  # 112.5s for HK (vs 45s default)
      - DB_QUERY_TIMEOUT=112500      # 112.5s for HK (vs 45s default)
      - DB_POOL_MAX=8                # Further reduced for single-CPU server
      - DB_POOL_MIN=1                # Minimal connections for single-CPU
      - DB_IDLE_TIMEOUT=90000        # 90s idle timeout for HK
      # Health check settings for HK
      - DB_HEALTH_CHECK_INTERVAL=45000  # 45s health check interval
      - DB_MAX_CONSECUTIVE_FAILURES=5   # Allow more failures before marking unhealthy
      - DB_MAX_RETRIES=5                # More retries for HK
      # Redis configuration (local Docker service)
      - REDIS_ENABLED=${REDIS_ENABLED:-true}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DEFAULT_TTL=${REDIS_DEFAULT_TTL:-7200}
      - REDIS_CONNECT_TIMEOUT=10000     # Increased Redis timeout for HK
      - REDIS_COMMAND_TIMEOUT=8000      # Increased Redis command timeout
      # Application settings optimized for single-CPU HK server
      - CONNECTION_WARMUP_INTERVAL=60000  # 60s warmup interval for HK
      - MEMORY_CACHE_SIZE=${MEMORY_CACHE_SIZE:-150}  # Reduced cache for single-CPU
      - MAX_MEMORY_CACHE_ITEMS=${MAX_MEMORY_CACHE_ITEMS:-750}  # Reduced for single-CPU
      # Logging settings for HK (more verbose for debugging)
      - LOG_LEVEL=info
      - ENABLE_REQUEST_LOGGING=true
      - ENABLE_PERFORMANCE_MONITORING=true  # Enable for HK debugging
      # Rate limiting adjusted for single-CPU HK server
      - RATE_LIMIT_MAX_REQUESTS=200         # Further reduced for single-CPU server
      - RATE_LIMIT_WINDOW_MS=60000
      # Other environment variables
      - API_URL=${API_URL}
      - INTERNAL_API_KEY=${INTERNAL_API_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - RAPIDAPI_PROXY_SECRET=${RAPIDAPI_PROXY_SECRET}
      - ADMIN_API_KEY=${ADMIN_API_KEY}
      - DB_AUTO_MIGRATE=${DB_AUTO_MIGRATE:-true}
      - DB_INIT_ON_START=${DB_INIT_ON_START:-true}
      - ENABLE_ATTACHMENTS=${ENABLE_ATTACHMENTS:-true}
      - ENABLE_DOMAIN_PRELOADING=${ENABLE_DOMAIN_PRELOADING:-true}
      - FORCE_CACHE=${FORCE_CACHE:-true}
      - FREE_USER_INBOX_LIMIT=${FREE_USER_INBOX_LIMIT:-10}
      - MAX_LOG_FILES=${MAX_LOG_FILES:-15}  # More log files for HK debugging
      - MAX_LOG_SIZE=${MAX_LOG_SIZE:-10}    # Larger log files for HK
    volumes:
      - type: bind
        source: ./logs
        target: /app/logs
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - tempmail-network

  redis:
    image: redis:7-alpine
    deploy:
      resources:
        limits:
          cpus: "0.2"  # Minimal CPU for Redis on single-CPU server
          memory: 512m # Reduced memory for Redis
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf", "--requirepass", "${REDIS_PASSWORD}"]
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "ping"]
      interval: 45s  # Increased for HK
      timeout: 10s   # Increased timeout
      retries: 5     # More retries
      start_period: 15s
    networks:
      - tempmail-network

volumes:
  redis_data:

networks:
  tempmail-network:
    driver: bridge
