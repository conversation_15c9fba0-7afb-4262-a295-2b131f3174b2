services:
  app1:
    build: .
    env_file: .env
    deploy:
      resources:
        limits:
          cpus: "2.5"  # Updated to 3 vCPU cores (allowing some overhead for system)
          memory: 6g   # Updated to use most of 8GB RAM (leaving 2GB for system/Redis)
    ports:
      - "3000:3000"
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/healthz"]
      interval: 45s  # Increased for India server stability
      timeout: 20s   # Increased timeout for India
      retries: 3     # Reduced retries to prevent restart loops
      start_period: 60s  # Longer start period for India
    # Add extra_hosts to allow container to access host services
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - INSTANCE_ID=india-1
      - REGION=india

      # CRITICAL FIX: Multi-region read/write splitting configuration
      # Write operations MUST go to US primary database (*************)
      # Read operations can use local India replica (host.docker.internal)
      - DB_WRITE_HOST=*************
      - DB_WRITE_PORT=5432
      - DB_WRITE_DATABASE=tempfly_app
      - DB_WRITE_USER=postgres
      - DB_WRITE_PASSWORD=4wyWCAAk92hkGUhdh7
      - DB_WRITE_SSLMODE=disable

      # Local India read replica for faster read operations
      - DB_READ_HOST=host.docker.internal
      - DB_READ_PORT=5432
      - DB_READ_DATABASE=tempfly_app
      - DB_READ_USER=postgres
      - DB_READ_PASSWORD=4wyWCAAk92hkGUhdh7
      - DB_READ_SSLMODE=disable

      # Legacy environment variables for backward compatibility
      - PGHOST=host.docker.internal
      - PGPORT=5432
      - PGDATABASE=tempfly_app
      - PGUSER=postgres
      - PGPASSWORD=4wyWCAAk92hkGUhdh7
      - PGSSLMODE=${PGSSLMODE:-disable}
      - DATABASE_URL=${DATABASE_URL}
      # India-specific database connection settings (optimized for 3 vCPU, 8GB RAM)
      - DB_CONNECTION_TIMEOUT=20000  # 20s for India (improved with better specs)
      - DB_ACQUIRE_TIMEOUT=10000     # 10s for India (improved performance)
      - DB_STATEMENT_TIMEOUT=90000   # 90s for India (improved with better specs)
      - DB_QUERY_TIMEOUT=90000       # 90s for India (improved with better specs)
      - DB_POOL_MAX=25               # Increased for 3 vCPU server with 8GB RAM
      - DB_POOL_MIN=4                # Increased minimum connections for better performance
      - DB_IDLE_TIMEOUT=90000        # 90s idle timeout for India (optimized)
      # Health check settings for India (optimized for better hardware)
      - DB_HEALTH_CHECK_INTERVAL=45000  # 45s health check interval (more frequent)
      - DB_MAX_CONSECUTIVE_FAILURES=6   # Balanced tolerance for failures
      - DB_MAX_RETRIES=4                # Optimized retries for India
      # Redis configuration (local Docker service)
      - REDIS_ENABLED=${REDIS_ENABLED:-true}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DEFAULT_TTL=${REDIS_DEFAULT_TTL:-7200}
      - REDIS_CONNECT_TIMEOUT=8000      # Optimized Redis timeout for India (better network)
      - REDIS_COMMAND_TIMEOUT=6000      # Optimized Redis command timeout
      # Application settings optimized for 3 vCPU, 8GB RAM India server
      - CONNECTION_WARMUP_INTERVAL=90000 # 90s warmup interval for India (optimized)
      - MEMORY_CACHE_SIZE=${MEMORY_CACHE_SIZE:-500}  # Increased cache for 8GB RAM
      - MAX_MEMORY_CACHE_ITEMS=${MAX_MEMORY_CACHE_ITEMS:-2500}  # Increased for 8GB RAM

      # Database Pool Configuration for India (Optimized for 3 vCPU, 8GB RAM)
      - DB_POOL_MAX=30                      # Increased to 30 for 3 vCPU and 8GB RAM capacity
      - DB_POOL_MIN=5                       # Increased minimum connections for better performance
      - DB_IDLE_TIMEOUT=120000              # 2 minutes idle timeout for India (optimized)
      - DB_CONNECTION_TIMEOUT=20000         # 20s connection timeout for India (improved)
      - DB_ACQUIRE_TIMEOUT=10000            # 10s acquire timeout for India (improved)
      - DB_STATEMENT_TIMEOUT=90000          # 90s statement timeout (improved with better specs)
      - DB_QUERY_TIMEOUT=90000              # 90s query timeout (improved with better specs)
      - DB_HEALTH_CHECK_INTERVAL=30000      # 30s health check interval (more frequent with better specs)
      - DB_MAX_CONSECUTIVE_FAILURES=4       # Optimized failure tolerance for India
      - DB_MAX_RETRIES=3                    # Optimized retries for India

      # Logging settings for India (more verbose for debugging)
      - LOG_LEVEL=info
      - ENABLE_REQUEST_LOGGING=true
      - ENABLE_PERFORMANCE_MONITORING=true  # Enable for India debugging
      # Rate limiting adjusted for 3 vCPU, 8GB RAM India server
      - RATE_LIMIT_MAX_REQUESTS=600         # Increased for 3 vCPU server with 8GB RAM
      - RATE_LIMIT_WINDOW_MS=60000
      # Other environment variables
      - API_URL=${API_URL}
      - INTERNAL_API_KEY=${INTERNAL_API_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - RAPIDAPI_PROXY_SECRET=${RAPIDAPI_PROXY_SECRET}
      - ADMIN_API_KEY=${ADMIN_API_KEY}
      - DB_AUTO_MIGRATE=${DB_AUTO_MIGRATE:-true}
      - DB_INIT_ON_START=${DB_INIT_ON_START:-true}
      - ENABLE_ATTACHMENTS=${ENABLE_ATTACHMENTS:-true}
      - ENABLE_DOMAIN_PRELOADING=${ENABLE_DOMAIN_PRELOADING:-true}
      - FORCE_CACHE=${FORCE_CACHE:-true}
      - FREE_USER_INBOX_LIMIT=${FREE_USER_INBOX_LIMIT:-10}
      - MAX_LOG_FILES=${MAX_LOG_FILES:-20}  # More log files for India debugging (8GB RAM)
      - MAX_LOG_SIZE=${MAX_LOG_SIZE:-15}    # Larger log files for India (8GB RAM)
    volumes:
      - type: bind
        source: ./logs
        target: /app/logs
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - tempmail-network

  redis:
    image: redis:7-alpine
    deploy:
      resources:
        limits:
          cpus: "0.5"  # Increased CPU for Redis on 3 vCPU server
          memory: 1.5g # Increased memory for Redis on 8GB RAM server
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf", "--requirepass", "${REDIS_PASSWORD}"]
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "ping"]
      interval: 45s  # Optimized for India stability with better specs
      timeout: 10s   # Optimized timeout with better performance
      retries: 3     # Balanced retries
      start_period: 15s
    networks:
      - tempmail-network

volumes:
  redis_data:

networks:
  tempmail-network:
    driver: bridge
