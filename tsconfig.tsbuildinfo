{"root": ["./src/index.ts", "./src/config/database-pools.ts", "./src/config/database.ts", "./src/config/knex.ts", "./src/config/limits.ts", "./src/config/prepared-statements.ts", "./src/config/redis-direct.ts", "./src/config/server.ts", "./src/config/smtp.ts", "./src/config/timeouts.ts", "./src/controllers/apikeycontroller.ts", "./src/controllers/domaincontroller.ts", "./src/controllers/emailcontroller.ts", "./src/controllers/inboxcontroller.ts", "./src/controllers/internalcontroller.ts", "./src/controllers/monitoringcontroller.ts", "./src/controllers/rapidapicontroller.ts", "./src/middlewares/adminauth.ts", "./src/middlewares/apikeyauth.ts", "./src/middlewares/auditloggermiddleware.ts", "./src/middlewares/cachemiddleware.ts", "./src/middlewares/combinedsecurityheaders.ts", "./src/middlewares/errorhandler.ts", "./src/middlewares/freeuserinboxlimiter.ts", "./src/middlewares/inboxownershipauth.ts", "./src/middlewares/internalapikeyauth.ts", "./src/middlewares/nocachemiddleware.ts", "./src/middlewares/payloadencryption.ts", "./src/middlewares/performancemonitoring.ts", "./src/middlewares/performanceoptimizer.ts", "./src/middlewares/rapidapiauth.ts", "./src/middlewares/rapidapiratelimiter.ts", "./src/middlewares/rapidapiusagetracker.ts", "./src/middlewares/ratelimiter.ts", "./src/middlewares/requestid.ts", "./src/middlewares/requestlogger.ts", "./src/middlewares/scannerprotection.ts", "./src/middlewares/securityheaders.ts", "./src/middlewares/timeoutmiddleware.ts", "./src/middlewares/validator.ts", "./src/models/apikey.ts", "./src/models/domain.ts", "./src/models/email.ts", "./src/models/inbox.ts", "./src/models/rapidapiusage.ts", "./src/routes/adminroutes.ts", "./src/routes/apikeyroutes.ts", "./src/routes/diagnostics.ts", "./src/routes/domainroutes.ts", "./src/routes/emailroutes.ts", "./src/routes/inboxroutes.ts", "./src/routes/internalroutes.ts", "./src/routes/monitoringroutes.ts", "./src/routes/rapidapiroutes.ts", "./src/services/smtpservice.ts", "./src/types/express/index.d.ts", "./src/utils/attachmentvalidator.ts", "./src/utils/auditlogger.ts", "./src/utils/backgroundtasks.ts", "./src/utils/circuitbreaker.ts", "./src/utils/connectionleakdetector.ts", "./src/utils/consolidatedhealthcheck.ts", "./src/utils/databaseconnectionmonitor.ts", "./src/utils/databasehealthmonitor.ts", "./src/utils/databaserouter.ts", "./src/utils/dateformatter.ts", "./src/utils/emailsender.ts", "./src/utils/encryption.ts", "./src/utils/errorboundary.ts", "./src/utils/healthcheck.ts", "./src/utils/localcache.ts", "./src/utils/logger.ts", "./src/utils/memorycache.ts", "./src/utils/randomgenerator.ts", "./src/utils/rotatinglogger.ts", "./src/utils/seeddomains.ts", "./src/utils/spamdetector.ts", "./src/utils/textcleaner.ts", "./src/utils/validateenv.ts", "./src/utils/validators.ts", "./src/validators/apikeyvalidators.ts", "./src/validators/emailvalidators.ts", "./src/validators/inboxvalidators.ts"], "version": "5.8.3"}